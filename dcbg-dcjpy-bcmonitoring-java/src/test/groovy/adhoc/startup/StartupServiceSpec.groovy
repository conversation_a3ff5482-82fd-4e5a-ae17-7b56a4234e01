package adhoc.startup

import adhoc.helper.AdhocHelper
import org.springframework.context.ConfigurableApplicationContext
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import spock.lang.Shared
import spock.lang.Specification

class StartupServiceSpec extends Specification {

	@Shared
	S3Client s3Client

	@Shared
	AdhocHelper.LogCapture logCapture
	@Shared
	ConfigurableApplicationContext applicationContext
	def logs = []

	private final String INVALID_SOCKET_PORT = "10101"

	def cleanupSpec() {
		AdhocHelper.resetLocalStack()
	}

	def setup() {
		logCapture = new AdhocHelper.LogCapture()
		logCapture.startCapture()
	}

	def cleanup() {
		AdhocHelper.closeApplication(applicationContext)
		applicationContext = null
	}

	def "should start service successfully and log startup messages"() {
		when: "Service is started"
		try {
			applicationContext = AdhocHelper.initApplication()
			Thread.sleep(2000) // Allow time for startup logs
		} finally {
			logs = logCapture.stopCaptureAndGetLogs()
		}

		then: "Application starts successfully"
		applicationContext != null
		applicationContext.isRunning()

		and: "Startup log messages are present"
		def startingLogFound = logs.any { it.contains("Starting bc monitoring") }
		def startedLogFound = logs.any { it.contains("Started bc monitoring") }

		startingLogFound && startedLogFound // Both startup logs must be present

		and: "Can read properties from application.properties"
		def environment = applicationContext.environment
		def serverPort = environment.getProperty("server.port")
		def appName = environment.getProperty("spring.application.name")

		serverPort != null || appName != null // At least one should be configured
	}


	def "should retry WebSocketHandshakeException up to 5 times"() {

		when: "Application is started with invalid WebSocket port"
		try {
			// DON'T set LocalStack endpoints - let it use default config like first test
			applicationContext = AdhocHelper.initApplication([
				"websocket.uri.port": INVALID_SOCKET_PORT // This will cause WebSocket connection to fail
			])
			Thread.sleep(2000) // Same as first test - just 2 seconds then finish
		} finally {
			logs = logCapture.stopCaptureAndGetLogs()
		}

		then: "Application starts successfully"
		applicationContext != null
		applicationContext.isRunning()

		and: "Startup log messages are present"
		def startingLogFound = logs.any { it.contains("Starting bc monitoring") }
		def startedLogFound = logs.any { it.contains("Started bc monitoring") }

		startingLogFound && startedLogFound // Both startup logs must be present

		and: "Retry logs are present (captured during the 2-second window)"
		def retryLogFound = logs.any { it.contains("restart bc monitoring") }

		// Debug output
		println "=== CAPTURED LOGS (${logs.size()} total) ==="
		logs.eachWithIndex { log, index ->
			println "Log ${index}: ${log}"
		}
		println "=== END LOGS ==="
		println "Retry log found: ${retryLogFound}"

		retryLogFound
	}

	def "Service starts when S3 bucket exists but contains no ABI files"() {
		given: "Log capture setup"

		s3Client = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()


		s3Client.listBuckets().buckets().each { bucket ->
			deleteAllObjectsFromBucket(bucket.name())
		}

		when: "Application is started"
		try {
			applicationContext = AdhocHelper.initApplication()
			Thread.sleep(2000) // Allow time for startup logs
		} finally {
			logs = logCapture.stopCaptureAndGetLogs()
		}
		then: "Application starts successfully"
		applicationContext != null
		applicationContext.isRunning()

		and: "Startup log messages are present"
		def startingLogFound = logs.any { it.contains("Starting bc monitoring") }
		def startedLogFound = logs.any { it.contains("Started bc monitoring") }

		startingLogFound || startedLogFound // At least one startup log should be present
	}

	def deleteAllObjectsFromBucket(String testBucketName) {
		println "Deleting all objects from bucket..."

		def objectsToDelete = []

		try {
			// List all objects
			ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
					.bucket(testBucketName)
					.build()

			def paginator = s3Client.listObjectsV2Paginator(listRequest)

			paginator.each { page ->
				if (page.contents()) {
					page.contents().each { s3Object ->
						objectsToDelete << ObjectIdentifier.builder()
								.key(s3Object.key())
								.build()
					}
				}
			}

			// Delete in batches of 1000
			if (objectsToDelete) {
				def totalDeleted = 0

				objectsToDelete.collate(1000).each { batch ->
					DeleteObjectsRequest deleteRequest = DeleteObjectsRequest.builder()
							.bucket(testBucketName)
							.delete(Delete.builder()
							.objects(batch)
							.build())
							.build()

					DeleteObjectsResponse response = s3Client.deleteObjects(deleteRequest)
					totalDeleted += response.deleted().size()
				}

				return totalDeleted
			}
		} catch (Exception e) {
			throw e
		}
		println "Deleting all objects from bucket successfully."
	}
}
