package adhoc.startup

import adhoc.helper.AdhocHelper
import org.springframework.context.ConfigurableApplicationContext
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import spock.lang.Shared
import spock.lang.Specification

class StartupServiceSpec extends Specification {

	@Shared
	S3Client s3Client

	@Shared
	AdhocHelper.LogCapture logCapture
	@Shared
	ConfigurableApplicationContext applicationContext
	def logs = []

	private final String INVALID_SOCKET_PORT = "10101"

	def cleanupSpec() {
		AdhocHelper.resetLocalStack()
	}

	def setup() {
		logCapture = new AdhocHelper.LogCapture()
		logCapture.startCapture()
	}

	def cleanup() {
		AdhocHelper.closeApplication(applicationContext)
		applicationContext = null
	}

	def "should start service successfully and log startup messages"() {
		when: "Service is started"
		try {
			applicationContext = AdhocHelper.initApplication()
			Thread.sleep(2000) // Allow time for startup logs
		} finally {
			logs = logCapture.stopCaptureAndGetLogs()
		}

		then: "Application starts successfully"
		applicationContext != null
		applicationContext.isRunning()

		and: "Startup log messages are present"
		def startingLogFound = logs.any { it.contains("Starting bc monitoring") }
		def startedLogFound = logs.any { it.contains("Started bc monitoring") }

		startingLogFound || startedLogFound // At least one startup log should be present

		and: "Can read properties from application.properties"
		def environment = applicationContext.environment
		def serverPort = environment.getProperty("server.port")
		def appName = environment.getProperty("spring.application.name")

		serverPort != null || appName != null // At least one should be configured
	}


	def "should test retry mechanism with controlled execution"() {

		when: "Retry template is tested directly"
		def retryTemplate = null
		def monitoringRetryListener = null
		def capturedLogs = []

		try {
			// Set environment variables to override LocalStack endpoints
			System.setProperty("LOCALSTACK_ENDPOINT", "http://localhost:" + AdhocHelper.getLocalStackPort())
			System.setProperty("DYNAMODB_ENDPOINT", "http://localhost:" + AdhocHelper.getLocalStackPort())

			applicationContext = AdhocHelper.initApplication([
				"env": "local",
				"logging.level.root": "INFO"
			])

			// Get the retry template and listener from the application context
			retryTemplate = applicationContext.getBean("monitoringRetryTemplate")
			monitoringRetryListener = applicationContext.getBean("monitoringRetryListener")

			// Capture logs during retry execution
			def originalOut = System.out
			def logCapture = new ByteArrayOutputStream()
			System.setOut(new PrintStream(logCapture))

			try {
				// Execute a task that will fail and trigger retries
				retryTemplate.execute({ context ->
					throw new com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig.Web3jConnectionException("Test WebSocket connection failure")
				})
			} catch (Exception e) {
				// Expected to fail after retries
			} finally {
				System.setOut(originalOut)
				capturedLogs = logCapture.toString().split('\n').toList()
			}

		} finally {
			logs = logCapture.stopCaptureAndGetLogs()
		}

		then: "Retry template and listener are available"
		retryTemplate != null
		monitoringRetryListener != null

		and: "Debug: Print captured retry logs"
		println "=== RETRY LOGS (${capturedLogs.size()} total) ==="
		capturedLogs.eachWithIndex { log, index ->
			println "Retry Log ${index}: ${log}"
		}
		println "=== END RETRY LOGS ==="

		and: "Retry logs contain restart message"
		def retryLogFound = capturedLogs.any { it.contains("restart bc monitoring") }
		println "Retry log found: ${retryLogFound}"
		retryLogFound
	}

	def "Service starts when S3 bucket exists but contains no ABI files"() {
		given: "Log capture setup"

		s3Client = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()


		s3Client.listBuckets().buckets().each { bucket ->
			deleteAllObjectsFromBucket(bucket.name())
		}

		when: "Application is started"
		try {
			applicationContext = AdhocHelper.initApplication()
			Thread.sleep(2000) // Allow time for startup logs
		} finally {
			logs = logCapture.stopCaptureAndGetLogs()
		}
		then: "Application starts successfully"
		applicationContext != null
		applicationContext.isRunning()

		and: "Startup log messages are present"
		def startingLogFound = logs.any { it.contains("Starting bc monitoring") }
		def startedLogFound = logs.any { it.contains("Started bc monitoring") }

		startingLogFound || startedLogFound // At least one startup log should be present
	}

	def deleteAllObjectsFromBucket(String testBucketName) {
		println "Deleting all objects from bucket..."

		def objectsToDelete = []

		try {
			// List all objects
			ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
					.bucket(testBucketName)
					.build()

			def paginator = s3Client.listObjectsV2Paginator(listRequest)

			paginator.each { page ->
				if (page.contents()) {
					page.contents().each { s3Object ->
						objectsToDelete << ObjectIdentifier.builder()
								.key(s3Object.key())
								.build()
					}
				}
			}

			// Delete in batches of 1000
			if (objectsToDelete) {
				def totalDeleted = 0

				objectsToDelete.collate(1000).each { batch ->
					DeleteObjectsRequest deleteRequest = DeleteObjectsRequest.builder()
							.bucket(testBucketName)
							.delete(Delete.builder()
							.objects(batch)
							.build())
							.build()

					DeleteObjectsResponse response = s3Client.deleteObjects(deleteRequest)
					totalDeleted += response.deleted().size()
				}

				return totalDeleted
			}
		} catch (Exception e) {
			throw e
		}
		println "Deleting all objects from bucket successfully."
	}
}
